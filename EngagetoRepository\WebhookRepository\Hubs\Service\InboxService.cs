﻿using AngleSharp.Dom;
using Engageto.Hubs;
using EngagetoContracts.WebhookContracts.Client;
using EngagetoDapper.Data.Dapper.Repositories.InboxRepositories;
using EngagetoEntities;
using EngagetoEntities.DdContext;
using EngagetoEntities.Dtos.ContactDtos;
using EngagetoEntities.Dtos.ConversationDtos;
using EngagetoEntities.Dtos.FilterDtos;
using EngagetoEntities.Dtos.MetaDto;
using EngagetoEntities.Dtos.WebhookDtos;
using EngagetoEntities.Entities;
using EngagetoEntities.Enums;
using EngagetoEntities.UserEntities.Models;
using EngagetoEntities.Utilities;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Linq.Expressions;
using System.Net;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;


namespace EngagetoRepository.WebhookRepository.Hubs.Service
{
    public class InboxService : IInboxService
    {
        private readonly HttpClient _httpClient;
        private readonly ApplicationDbContext appDbContext;
        private readonly ApplicationDBContext _applicationDBContext;
        private readonly IWhatsAppBusinessClient _whatsAppBusinessClient;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration configuration;
        private readonly HttpClient _client = new HttpClient();
        SendHandler Handler = new SendHandler();
        private readonly JsonSerializer _serializer = new JsonSerializer();
        private IHubContext<MessageHub, IMessageHubClient> messageHub;
        private readonly EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IInboxService _inboxService;
        private readonly IInboxRepository _inboxRepository;

        public InboxService(IConfiguration config,
            IHubContext<MessageHub, IMessageHubClient> message,
            HttpClient httpClient,
            ApplicationDbContext appDbContext,
            ApplicationDBContext applicationDBContext,
            IWhatsAppBusinessClient whatsAppBusinessClient,
            IHttpClientFactory httpClientFactory,
            EngagetoDapper.Data.Interfaces.IUserInterfaces.IUserServices.IInboxService inboxService,
            IInboxRepository inboxRepository)
        {
            _httpClient = httpClient;
            this.appDbContext = appDbContext;
            _whatsAppBusinessClient = whatsAppBusinessClient;
            _httpClientFactory = httpClientFactory;
            messageHub = message;
            configuration = config;
            _inboxService = inboxService;
            _applicationDBContext = applicationDBContext;
            _inboxRepository = inboxRepository;
        }

        public async Task<object> LatestInboxContacts(Guid businessId, string userId, FilterDto operations, int page)
        {
            page = page <= 0 ? 1 : page;
            int pageSize = 30;
            var result = new List<InboxContactDto>();
            int totalCount = 0;
            int totalContact = 0;
            await UpdateExpiredChatStatusesAsync();
            var UserIdExist = _applicationDBContext.UserRoles;
            var normalizedBusinessId = businessId.ToString().ToLower();
            IQueryable<Contacts> query = appDbContext.Contacts.Where(x => x.BusinessId == businessId);
            var user = UserIdExist.FirstOrDefault(m => m.Id.ToString() == userId);
            if (user != null)
            {
                var role = await _applicationDBContext.Roles
                                                      .Where(r => r.Id == user.RoleId)
                                                      .Select(r => r.Name)
                                                      .FirstOrDefaultAsync();

                if (role == "Admin" || role == "Owner")
                {
                    query = query.Where(m => m.IsActive);
                    totalContact = await _inboxRepository.GetConversationCountAsync(businessId, role);
                }
                else
                {
                    query = query.Where(m => m.UserId.ToString() == userId);
                    Guid? parsedUserId = Guid.TryParse(userId, out var guid) ? guid : (Guid?)null;
                    totalContact = await _inboxRepository.GetConversationCountAsync(businessId, role, parsedUserId);
                }

            }

            if (!string.IsNullOrEmpty(operations.Searching?.Value))
            {
                string searchValue = operations.Searching.Value.ToLower();
                query = query.Where(contact =>
                    contact.Name.ToLower().Contains(searchValue) ||
                    (contact.CountryCode + contact.Contact).Replace("+", "").Contains(searchValue));
            }
            bool IsUnreadFilter = operations.Filtering?.Conditions?.Any(i => i.Column == "MessageStatus" && i.Value == "unread") ?? false;
            if (operations.Filtering?.Conditions != null && operations.Filtering.Conditions.Any())
            {
                var parameter = Expression.Parameter(typeof(Contacts), "contact");
                Expression combinedExpression = Expression.Constant(true);

                foreach (var condition in operations.Filtering.Conditions)
                {
                    if (condition.Column == "MessageStatus" && condition.Value == "unread")
                    {
                        var unreadNumbersQuery = appDbContext.Conversations
                            .Where(x => x.Status == ConvStatus.received && x.To == businessId.ToString())
                            .Select(x => x.From.Replace("+", ""))
                            .Distinct()
                            .AsNoTracking();

                        query = from contact in query
                                join unread in unreadNumbersQuery
                                on (EF.Functions.Like(contact.CountryCode, "+%") ? contact.CountryCode.Substring(1) : contact.CountryCode)
                                   + contact.Contact equals unread
                                select contact;
                        continue;
                    }
                    if (condition.Column != "MessageStatus")
                    {
                        string columnName = condition.Column ?? string.Empty;

                        if (columnName.Equals("Assignee", StringComparison.OrdinalIgnoreCase))
                        {
                            var userIdProperty = Expression.Property(parameter, "UserId");

                            if (string.IsNullOrWhiteSpace(condition.Value))
                            {
                                var nullConstant = Expression.Constant(null, typeof(Guid?));
                                var isNullExpression = Expression.Equal(userIdProperty, nullConstant);
                                combinedExpression = Expression.AndAlso(combinedExpression, isNullExpression);
                            }
                            else
                            {
                                var valueConstant = Expression.Constant(Guid.Parse(condition.Value.ToString()), typeof(Guid?));
                                var equals = Expression.Equal(userIdProperty, valueConstant);
                                combinedExpression = Expression.AndAlso(combinedExpression, equals);
                            }

                            continue;
                        }


                        if (columnName.Equals("Tags", StringComparison.OrdinalIgnoreCase))
                        {
                            var tagsProperty = Expression.Property(parameter, "Tags");
                            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });
                            var valueExpression = Expression.Constant(condition.Value?.ToString());
                            var containsExpression = Expression.Call(tagsProperty, containsMethod, valueExpression);

                            combinedExpression = combinedExpression == null
                                ? containsExpression
                                : Expression.AndAlso(combinedExpression, containsExpression);

                            continue;
                        }
                        var property = Expression.Property(parameter, columnName);

                        if (property.Type.IsEnum)
                        {
                            var enumValue = Enum.Parse(property.Type, condition.Value?.ToString() ?? string.Empty, true);
                            var value = Expression.Constant(enumValue);
                            var equals = Expression.Equal(property, Expression.Convert(value, property.Type));
                            combinedExpression = Expression.AndAlso(combinedExpression, equals);
                            continue;
                        }

                        if (property.Type == typeof(bool?) && !string.IsNullOrEmpty(condition.Value))
                        {
                            if (string.IsNullOrWhiteSpace(condition.Value))
                            {
                                var nullConstant = Expression.Constant(null, typeof(bool?));
                                var isNullExpression = Expression.Equal(property, nullConstant);
                                combinedExpression = Expression.AndAlso(combinedExpression, isNullExpression);
                            }
                            else if (bool.TryParse(condition.Value.ToString(), out bool boolValue))
                            {
                                var value = Expression.Constant(boolValue, typeof(bool?));
                                var equals = Expression.Equal(property, value);
                                combinedExpression = Expression.AndAlso(combinedExpression, equals);
                            }
                            continue;
                        }

                        if (!string.IsNullOrEmpty(condition.Value) && property.Type == typeof(Guid?) && Guid.TryParse(condition.Value.ToString(), out Guid guidValue))
                        {
                            var userIdProperty = Expression.Property(parameter, "UserId");
                            var valueConstant = Expression.Constant(condition.Value.ToString().ToLower(), typeof(string));
                            var toStringMethod = typeof(object).GetMethod("ToString");
                            var userIdAsString = Expression.Call(Expression.Convert(userIdProperty, typeof(object)), toStringMethod);
                            var equals = Expression.Equal(
                                Expression.Call(userIdAsString, toStringMethod),
                                valueConstant
                            );

                            combinedExpression = Expression.AndAlso(combinedExpression, equals);
                            continue;
                        }
                        var constant = Expression.Constant(condition.Value);
                        var equalsExpression = Expression.Equal(property, constant);
                        combinedExpression = Expression.AndAlso(combinedExpression, equalsExpression);
                    }
                }

                var lambda = Expression.Lambda<Func<Contacts, bool>>(combinedExpression, parameter);
                query = query.Where(lambda);
            }

            if (operations.DateRangeFilters != null && operations.DateRangeFilters.Any())
            {
                foreach (var dateFilter in operations.DateRangeFilters)
                {
                    if (dateFilter.Column == "CreatedDate")
                    {
                        if (DateTime.TryParse(dateFilter.FromDate, out DateTime fromDate) &&
                            DateTime.TryParse(dateFilter.ToDate, out DateTime toDate))
                        {
                            toDate = toDate.Date.AddDays(1);
                            query = query.Where(c => c.CreatedDate >= fromDate && c.CreatedDate < toDate);
                        }
                    }

                    if (dateFilter.Column == "LastMessageAt")
                    {
                        if (DateTime.TryParse(dateFilter.FromDate, out DateTime fromDate) &&
                            DateTime.TryParse(dateFilter.ToDate, out DateTime toDate))
                        {
                            toDate = toDate.Date.AddDays(1);
                            query = query.Where(c => c.LastMessageAt >= fromDate && c.LastMessageAt < toDate);
                        }
                    }
                }
            }

            bool isValue = operations.Filtering != null && operations.Filtering.Conditions != null ? operations.Filtering.Conditions.Any(i => i.Value != null) : false;
            bool isFiltersExist = (operations.Searching != null && !string.IsNullOrEmpty(operations.Searching.Value))
                || (operations.Sorting != null && (!string.IsNullOrEmpty(operations.Sorting.Column) && !string.IsNullOrEmpty(operations.Sorting.Order)))
                || (operations.Filtering != null && operations.Filtering.Conditions != null && isValue) || (operations.DateRangeFilters != null && operations.DateRangeFilters.Any());


            var pagedContactsList = new List<Contacts>();
            if (isFiltersExist)
            {
                pagedContactsList = await query
                    .OrderByDescending(m => m.LastMessageAt ?? m.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();
            }
            else
            {
                pagedContactsList = await query.Where(m => m.LastMessageAt != null)
                    .OrderByDescending(m => m.LastMessageAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .AsNoTracking()
                    .ToListAsync();
            }

            pagedContactsList = pagedContactsList
                 .OrderByDescending(c => c.LastMessageAt)
                 .ToList();

            var contactNumbers = pagedContactsList
                .Select(c => (c.CountryCode + c.Contact).Replace("+", ""))
                .ToList();

            var latestConversations = await appDbContext.Conversations
                           .Where(conversation => (contactNumbers.Contains(conversation.From) || contactNumbers.Contains(conversation.To))
                                                  && (conversation.From == businessId.ToString() || conversation.To == businessId.ToString())
                                                  && (conversation.TextMessage != null || conversation.TemplateBody != null || conversation.MediaCaption != null))
                           .GroupBy(conversation => contactNumbers.Contains(conversation.From) ? conversation.From : conversation.To)
                           .Select(g => new
                           {
                               ContactNumber = g.Key,
                               LatestConversation = g.OrderByDescending(c => c.CreatedAt).FirstOrDefault()
                           }).ToDictionaryAsync(x => x.ContactNumber, x => x.LatestConversation);

            // Fetch Unread Counts in a Single Query
            var unreadCounts = await appDbContext.Conversations
                .Where(conversation =>
                    contactNumbers.Contains(conversation.From) &&
                    conversation.To.ToLower() == normalizedBusinessId &&
                    conversation.Status == ConvStatus.received)
                .GroupBy(conversation => conversation.From)
                .Select(g => new { ContactNumber = g.Key, UnreadCount = g.Count() })
                .ToDictionaryAsync(x => x.ContactNumber, x => x.UnreadCount);

            // Build the final paginated contact list with additional data
            var pagedContacts = pagedContactsList.Select(contact => new
            {
                Contact = contact,
                ContactNumber = (contact.CountryCode + contact.Contact).Replace("+", ""),
                LatestConversation = latestConversations.TryGetValue((contact.CountryCode + contact.Contact).Replace("+", ""), out var latestConv)
                    ? latestConv
                    : null,
                UnreadCount = unreadCounts.TryGetValue((contact.CountryCode + contact.Contact).Replace("+", ""), out var unreadCount)
                    ? unreadCount
                    : 0
            }).ToList();

            result = pagedContacts.Select(x => new InboxContactDto
            {
                ContactId = x.Contact.ContactId,
                Contact = x.ContactNumber,
                Name = x.Contact.Name,
                LastMessage = x.LatestConversation != null
                              ? !string.IsNullOrEmpty(x.LatestConversation.TextMessage)
                                   ? x.LatestConversation.TextMessage
                                   : (x.LatestConversation.MediaMimeType != null ? GetMediaDescription(x.LatestConversation.MediaMimeType) : "📝 " + x.LatestConversation.TemplateBody ?? string.Empty)
                             : string.Empty,
                LastMessageAt = x.LatestConversation?.CreatedAt != null ? DateTime.UtcNow - x.LatestConversation.CreatedAt : (TimeSpan?)null,
                IsSpam = x.Contact.IsSpam ?? false ? true : false,
                UnRead = x.UnreadCount,
                ChatStatus = x.Contact.ChatStatus.ToString() == EngagetoEntities.Enums.ChatStatus.New.ToString() ? "new" : x.Contact.ChatStatus.ToString(),
                Tags = x.Contact.Tags?.Split(',').Where(s => !string.IsNullOrEmpty(s)).ToArray() ?? Array.Empty<string>(),
                IsOptIn = (int)x.Contact.IsOptIn,
                Assignee = x.Contact.UserId?.ToString() ?? ""
            }).ToList();

            if (operations?.Sorting != null && !string.IsNullOrEmpty(operations.Sorting.Column))
            {
                totalContact = 0;
                var property = typeof(InboxContactDto).GetProperty(operations.Sorting.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property != null)
                {
                    result = operations.Sorting.Order == "desc"
                        ? result.OrderByDescending(m => property.GetValue(m)).ToList()
                        : result.OrderBy(m => property.GetValue(m)).ToList();
                }
            }

            result = result
               .DistinctBy(m => m.Contact)
               .ToList();

            if (operations != null && isFiltersExist)
                totalContact = 0;

            totalCount = totalContact > 0 ? totalContact : await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            return new
            {
                Data = result,
                TotalCount = totalCount,
                TotalPages = totalPages,
                PageSize = pageSize,
                CurrentPage = page
            };
        }

        public async Task<long> GetUnreadMessageCountAsync(Guid businessId)
        {
            long unreadCounts = await appDbContext.Conversations.CountAsync(conversation => (conversation.To.ToLower() == businessId.ToString().ToLower() && conversation.Status == ConvStatus.received));
            return unreadCounts;
        }
        public async Task<List<InboxContactDto>> GetInboxContactsAsync(Guid businessId, Guid userId, FilterDto? operations)
        {
            List<Contacts> data = new List<Contacts>();
            string Role = "";

            if (appDbContext.Users.Select(m => m.Id).Contains(userId))
            {
                var RoleId = (await appDbContext.Users.FirstOrDefaultAsync(m => m.Id == userId))?.RoleId;
                _ = Guid.TryParse(RoleId, out Guid roleId);
                var Data = await _applicationDBContext.Roles.FirstOrDefaultAsync(m => m.Id == roleId);
                if (Data != null)
                {
                    Role = Data.Name;
                }
                else
                {
                    throw new Exception("Please enter a valid UserId.");
                }
                if (Role == "Admin" || Role == "Owner")
                {
                    data = await appDbContext.Contacts.Where(m => m.BusinessId == businessId && m.IsActive == true).ToListAsync();
                }
                else
                {
                    data = await appDbContext.Contacts.Where(m => m.BusinessId == businessId && m.IsActive == true).Where(m => m.UserId == userId).ToListAsync();
                }
            }
            else
            {
                throw new Exception("Please enter a valid UserId.");
            }
            List<InboxContactDto> Result = new List<InboxContactDto>();
            var BusinessIds = appDbContext.BusinessDetailsMetas.Select(x => x.BusinessId.ToLower()).ToList();
            string WhatsappPhoneNumberId = "";
            var WhatsappBusiness = appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == businessId.ToString());
            if (WhatsappBusiness == null)
            {
                throw new Exception("Please enter a valid BusinessId.");

            }
            else
            {

                WhatsappPhoneNumberId = WhatsappBusiness.PhoneNumberID;

                var Conversations = appDbContext.Conversations.Where(m => m.From == WhatsappPhoneNumberId || m.To == WhatsappPhoneNumberId).Select(m => m.From).ToList();
                Conversations.AddRange(appDbContext.Conversations.Where(m => m.From == WhatsappPhoneNumberId || m.To == WhatsappPhoneNumberId).Select(m => m.To).ToList());

                var contacts = data.Select(m => new { Contact = m.CountryCode.Replace("+", "") + m.Contact }).ToList();
                // var unknownNumbers = Conversations.Except(contacts.Select(c => c.Contact)).ToList();

                // Assuming data is a collection of contacts and appDbContext is your DbContext instance

                foreach (var contact in data)
                {
                    // Construct phone number
                    var phoneNumber = contact.CountryCode + contact.Contact;
                    string phoneNumberId = phoneNumber.Replace("+", "");

                    // Determine the name
                    var name = !string.IsNullOrEmpty(contact.Name) ? contact.Name : phoneNumber;

                    // Initialize variables for last message and last Conversations
                    var Conversatios = appDbContext.Conversations;
                    var lastMessage = "";
                    // Get the last Conversations for this contact
                    var lastConversations = Conversatios.Where(m => (m.From == phoneNumberId && m.To == WhatsappPhoneNumberId) || (m.To == phoneNumberId && m.From == WhatsappPhoneNumberId)).OrderByDescending(m => m.CreatedAt).FirstOrDefault();


                    if (lastConversations != null)
                    {


                        if (lastConversations.TextMessage != null)
                            lastMessage += lastConversations.TextMessage;
                        else if (lastConversations.MediaMimeType != null)
                        {


                            string mediaMessage = lastConversations.MediaFileName ?? lastConversations.MediaMimeType;

                            // Check the type of media and assign an appropriate icon
                            if (lastConversations.MediaMimeType.StartsWith("image/"))
                            {
                                lastMessage += "🖼️ Photo"; // Display "Photo" with a camera emoji
                            }
                            else if (lastConversations.MediaMimeType.StartsWith("video/"))
                            {
                                lastMessage += "🎥 Video"; // Display "Video" with a video camera emoji
                            }
                            else if (lastConversations.MediaMimeType.StartsWith("audio/"))
                            {
                                lastMessage += "🎧 Audio"; // Display "Video" with a video camera emoji
                            }
                            else
                            {
                                lastMessage += mediaMessage; // Use the media message as it is
                            }

                            // If MediaFileName is not null, include a document symbol along with the message
                            if (lastConversations.MediaFileName != null)
                            {
                                lastMessage += "📄 " + lastMessage; // Add a document symbol at the front
                            }

                        }
                        else
                        {
                            lastMessage += "📝 " + lastConversations.TemplateBody;
                        }
                    }
                    var NotRead = appDbContext.Conversations.Where(m => m.From == phoneNumberId && m.To == WhatsappPhoneNumberId && m.Status == 0).Count();

                    if (lastConversations != null)
                    {
                        var lastMessageReceived = Conversatios.Where(m => m.From == phoneNumberId && m.To == WhatsappPhoneNumberId).OrderByDescending(m => m.CreatedAt).FirstOrDefault();


                    }

                    // Create the result object
                    InboxContactDto result = new InboxContactDto
                    {
                        Contact = phoneNumberId,
                        Name = name,
                        ContactId = contact.ContactId,
                        ChatStatus = contact.ChatStatus.ToString() == ChatStatus.New.ToString() ? "new" : contact.ChatStatus.ToString(),
                        Assignee = contact.UserId?.ToString() ?? "",
                        Tags = contact?.Tags?.Split(',')
                           .Where(s => !string.IsNullOrEmpty(s))
                           .ToArray() ?? Array.Empty<string>(),
                        LastMessageAt = lastConversations != null ? DateTime.UtcNow - lastConversations.CreatedAt : (TimeSpan?)null,
                        LastMessage = lastMessage,
                        UnRead = NotRead,
                        IsSpam = contact.IsSpam,
                        IsOptIn = (int)contact.IsOptIn
                    };

                    Result.Add(result);
                }
            }
            // Order by LastMessageAt, placing null values at the end
            Result = Result.OrderBy(m => m.LastMessageAt == null ? 1 : 0).ThenBy(m => m.LastMessageAt).ToList(); // Step 2: For non-nulls, sort in ascending order


            Filters filter = new Filters();

            if (operations != null)
            {

                var filtered = false;
                if (operations.Searching != null)
                    if (operations.Searching.Value != null && operations.Searching.Value != "")
                    {
                        var data1 = Result.Where(m => m.Name.ToLower().Contains(operations.Searching.Value.ToLower())).ToList();
                        Result = data1;
                    }
                if (operations.Sorting != null)
                {

                    filtered = true;
                    if (operations.Sorting.Column != "" && operations.Sorting.Column != null)
                    {
                        operations.Sorting.Column = operations.Sorting.Column.ToLower();
                        operations.Sorting.Order = operations.Sorting.Order.ToLower();

                        Result = operations.Sorting.Order == "desc" ?
                            Result.OrderByDescending(m => GetValue(m, operations.Sorting.Column)).ToList() :
                            Result.OrderBy(m => GetValue(m, operations.Sorting.Column)).ToList();
                        filter.Sort = operations.Sorting.Column;
                        object GetValue(object item, string propertyName)
                        {
                            var property = item.GetType().GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                var value = property.GetValue(item, null);
                                // Handle if property is DateTime
                                if (value is DateTime dateTimeValue)
                                {
                                    return dateTimeValue;
                                }
                                return value; // Return as is for non-DateTime properties
                            }
                            throw new ArgumentException($"Property '{propertyName}' not found.");
                        }
                    }
                }
                if (operations.Filtering != null)
                {
                    filtered = true;
                    if (operations.Filtering.FilterType != null && operations.Filtering.FilterType.Any())
                    {
                        List<InboxContactDto> filteredList = Result;
                        List<InboxContactDto> Empty = new List<InboxContactDto>();

                        if (operations.Filtering.FilterType.ToLower() == "and")
                        {
                            foreach (var condition in operations.Filtering.Conditions)
                            {
                                //condition.Column = condition.Column.ToLower();
                                // ... (Code to map column names)
                                var property = typeof(InboxContactDto).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                                if (property != null)
                                {
                                    filteredList = ApplyCondition(filteredList, condition, property);
                                    switch (condition?.Column.ToLower())
                                    {
                                        case "assignee":
                                            filter.Assignee = condition?.Value;
                                            break;
                                        case "chatstatus":
                                            filter.ChatStatus = condition?.Value;
                                            break;
                                        case "tags":
                                            filter.Tags = condition?.Value;
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                        else if (operations.Filtering.FilterType == "or")
                        {
                            var tempFilteredList = new List<InboxContactDto>();
                            filteredList = new List<InboxContactDto>();
                            foreach (var condition in operations.Filtering.Conditions)
                            {
                                // condition.Column = condition.Column.ToLower();
                                // ... (Code to map column names)

                                var property = typeof(InboxContactDto).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                                if (property != null)
                                {
                                    tempFilteredList = ApplyCondition(Result, condition, property);
                                    foreach (var item in tempFilteredList)
                                    {
                                        if (!filteredList.Contains(item))
                                        {
                                            filteredList.Add(item);
                                        }
                                    }
                                    switch (condition?.Column.ToLower())
                                    {
                                        case "assignee":
                                            filter.Assignee = condition?.Value;
                                            break;
                                        case "chatstatus":
                                            filter.ChatStatus = condition?.Value;
                                            break;
                                        case "tags":
                                            filter.Tags = condition?.Value;
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                        Result = filteredList;
                    }
                }
                if (filtered)
                {
                    var FilterId = appDbContext.Filters.Select(m => m.UserId);
                    if (FilterId != null)
                    {
                        if (FilterId.Contains(userId))
                        {
                            var pre_filter = appDbContext.Filters.FirstOrDefault(m => m.UserId == userId);
                            pre_filter.Assignee = filter.Assignee;
                            pre_filter.ChatStatus = filter.ChatStatus;
                            pre_filter.Tags = filter.Tags;
                            pre_filter.Sort = filter.Sort;
                            appDbContext.Filters.Update(pre_filter);
                            appDbContext.SaveChanges();
                        }
                        else
                        {
                            filter.UserId = userId;
                            appDbContext.Filters.Add(filter);
                            appDbContext.SaveChanges();
                        }
                    }
                }
            }
            return Result;
        }

        public async Task<bool> BookDemoAsync(string name, string email, DateTime scheduledTime)
        {
            if (string.IsNullOrEmpty(email))
            {
                throw new Exception("Invalid email format.");
            }
            if (string.IsNullOrEmpty(name) || name.Length <= 3)
            {
                throw new Exception("Name should contain more than 3 characters.");
            }

            string emailPattern = @"^[^\s@]+@[^\s@]+\.[^\s@]+$";
            if (!Regex.IsMatch(email, emailPattern))
            {
                throw new Exception("Invalid email format.");
            }

            if (!(scheduledTime > DateTime.Now))
            {
                throw new Exception("Please provide a valid schedule time.");
            }
            string roomName = Guid.NewGuid().ToString();

            string roomUrl = await CreateDailyCoRoom(roomName, scheduledTime);

            await SendEmailAsync(email, name, roomUrl, scheduledTime);
            string reminderEmail = configuration["ReminderSettings:DemoReminderMail"] ?? string.Empty;
            await SendReminderEmailAsync(name, roomUrl, scheduledTime);

            return true;
        }

        private async Task SendEmailAsync(string toEmail, string toName, string roomUrl, DateTime ScheduleTime)
        {
            var Server = configuration["SmtpSettings:SmtpServer"];
            var Port = configuration["SmtpSettings:SmtpPort"];
            var Password = configuration["SmtpSettings:SmtpPassword"];
            var From = configuration["SmtpSettings:SmtpUsername"];
            var Logo = configuration["SmtpSettings:LogoUrl"];

            using var client = new SmtpClient(Server, Convert.ToInt32(Port))
            {
                Credentials = new NetworkCredential(From, Password),
                EnableSsl = true
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(From),
                Subject = $"Invitation: Join Us for a Demo Meeting on Engageto at {ScheduleTime.ToString("HH:mm")} on {ScheduleTime.ToString("dddd, MMMM d, yyyy")}",
                Body = $@"
<body>
    <table style='width: 100%; background-color: rgba(255, 255, 255, 0.8); border-collapse: collapse; margin: 0 auto; padding: 20px;'>
        <tr>
            <td style='text-align: center;'>
                <span style='font-size: 24px; color: #2980b9;'>Engageto</span>
            </td>
        </tr>
        <tr>
            <td style='padding: 20px;'>
                <p style='color: #333;'>Dear {toName},</p>
                <p style='color: #333;'>I hope this email finds you well.</p>
                <p style='color: #333;'>We are excited to extend an invitation to you for an exclusive demo of our innovative Engageto, designed to <span style='color: #2980b9;'>engage your customers efficiently</span>.</p>
                <p style='color: #333;'>This demonstration will provide you with valuable insights into how our solution can solve a problem and increase efficiency.</p>
                <h3 style='color: #2980b9;'>Meeting Details:</h3>
                <ul style='color: #333; list-style: none; padding: 0;'>
                    <li>Date: {ScheduleTime.ToString("dddd, MMMM d, yyyy")}</li>
                    <li>Time: {ScheduleTime.ToString("HH:mm")} IST (Indian Standard Time)</li>
                    <li>Duration: 60 minutes</li>
                    <li>Platform: Daily.Co</li>
                    <li>Meeting Link: <a href='{roomUrl}' style='color: #2980b9; text-decoration: none;'>{roomUrl}</a></li>
                </ul>
                <p style='color: #333;'>During this demo, we will:</p>
                <ul style='color: #333; list-style: none; padding: 0;'>
                    <li>- Introduce the main features and benefits of Engageto.</li>
                    <li>- Showcase a live demonstration of how it works.</li>
                    <li>- Discuss how it can specifically benefit your organization.</li>
                    <li>- Answer any questions you may have in real-time.</li>
                </ul>
                <p style='color: #333;'>We believe that Engageto can significantly contribute to engage your customers efficiently, and we are keen to show you how it can be tailored to your needs.</p>
                <p style='color: #333;'>Please confirm your attendance by replying to this email. If the proposed time does not work for you, let us know your availability, and we will do our best to accommodate.</p>
                <p style='color: #333;'>Thank you for considering this invitation. We look forward to demonstrating how Engageto can make a positive impact on your operations.</p>
                <p style='color: #333;'>Best regards,</p>
                <p style='color: #2980b9;'>Engageto</p>
                <p style='color: #333;'>{From}</p>
            </td>
        </tr>
    </table>
</body>",

                IsBodyHtml = true,


            };

            mailMessage.To.Add(toEmail);

            await client.SendMailAsync(mailMessage);
        }

        private async Task SendReminderEmailAsync(string toEmail, string roomUrl, DateTime scheduleTime)
        {
            var server = configuration["SmtpSettings:SmtpServer"];
            var port = configuration["SmtpSettings:SmtpPort"];
            var password = configuration["SmtpSettings:SmtpPassword"];
            var from = configuration["SmtpSettings:SmtpUsername"];
            var logo = configuration["SmtpSettings:LogoUrl"];
            using var client = new SmtpClient(server, Convert.ToInt32(port))
            {
                Credentials = new NetworkCredential(from, password),
                EnableSsl = true
            };

            var demoConductorEmail = configuration["DemoConductor:Email"];
            var demoConductorName = configuration["DemoConductor:Name"];

            var mailMessage = new MailMessage
            {
                From = new MailAddress(from),
                Subject = $"Reminder: Demo Meeting on Engageto at {scheduleTime.ToString("HH:mm")} on {scheduleTime.ToString("dddd, MMMM d, yyyy")}",
                Body = $@"
<div style='height: 100vh; display: flex; align-items: center; justify-content: center;'>
    <div style='font-family: Arial, sans-serif; background-color: #f7f7f7; padding: 20px; max-width: 600px; margin: 0 auto;'>
        <div style='text-align: center; margin-bottom: 10px;'>
            <img src='{logo}' alt='Engageto Logo' style='width: 200px;' />
        </div>
        <div style='background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); overflow: hidden;'>
            <div style='background-color: #0066cc; color: #ffffff; padding: 20px 30px; text-align: center;'>
                <h1 style='margin: 0; font-size: 24px;'>Engageto Demo Meeting Reminder</h1>
            </div>
            <div style='padding: 20px 30px;'>
                <p style='font-size: 16px; color: #555555;'>Dear <span style='font-weight: bold;'>{demoConductorName}</span>,</p>
                <p style='font-size: 16px; color: #555555;'>This is a friendly reminder about your upcoming demo meeting of our innovative Engageto platform.</p>
                <div style='background-color: #f4f4f4; padding: 15px; border-radius: 8px; margin-top: 20px;'>
                    <h2 style='color: #0066cc; font-size: 20px; border-bottom: 2px solid #0066cc; padding-bottom: 10px;'>Meeting Details</h2>
                    <ul style='list-style-type: none; padding: 0; margin: 20px 0;'>
                        <li style='margin-bottom: 10px;'><strong>Date:</strong> {scheduleTime.ToString("dddd, MMMM d, yyyy")}</li>
                        <li style='margin-bottom: 10px;'><strong>Time:</strong> {scheduleTime.ToString("HH:mm")} IST (Indian Standard Time)</li>
                        <li style='margin-bottom: 10px;'><strong>Duration:</strong> 60 minutes</li>
                        <li style='margin-bottom: 10px;'><strong>Platform:</strong> Daily.Co</li>
                        <li style='margin-bottom: 10px;'><strong>Meeting Link:</strong> <a href='{roomUrl}' style='color: #0066cc; text-decoration: none;'>{roomUrl}</a></li>
                    </ul>
                </div>
                <p style='font-size: 16px; color: #555555; margin-top: 20px;'>We look forward to your participation.</p>
                <p style='font-size: 16px; color: #555555;'>Best regards,<br />Engageto Team<br />{from}</p>
            </div>
        </div>
    </div>
</div>
",

                IsBodyHtml = true
            };

            mailMessage.To.Add(demoConductorEmail);

            await client.SendMailAsync(mailMessage);
        }

        private async Task<string> CreateDailyCoRoom(string roomName, DateTime scheduledTime)
        {
            var payload = new
            {
                name = roomName,
                properties = new
                {
                    enable_chat = true,
                    enable_knocking = false,
                    start_video_off = false,
                    exp = ((DateTimeOffset)scheduledTime.AddHours(2)).ToUnixTimeSeconds()
                }
            };
            var _baseUrl = configuration["DailyCo:BaseUrl"];
            var _apiKey = configuration["DailyCo:ApiKey"];
            string jsonPayload = Newtonsoft.Json.JsonConvert.SerializeObject(payload);
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}rooms")
            {
                Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json")
            };
            request.Headers.Add("Authorization", $"Bearer {_apiKey}");

            HttpResponseMessage response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            string responseBody = await response.Content.ReadAsStringAsync();
            var responseJson = JObject.Parse(responseBody);

            return responseJson["url"]?.ToString() ?? string.Empty;
        }

        private string GetMediaDescription(string mediaMimeType)
        {
            if (string.IsNullOrEmpty(mediaMimeType))
                return "Unknown Media";

            if (mediaMimeType.StartsWith("image/", StringComparison.OrdinalIgnoreCase))
                return "🖼️ Photo";
            if (mediaMimeType.StartsWith("video/", StringComparison.OrdinalIgnoreCase))
                return "🎥 Video";
            if (mediaMimeType.StartsWith("audio/", StringComparison.OrdinalIgnoreCase))
                return "🎧 Audio";
            if (mediaMimeType.StartsWith("application/", StringComparison.OrdinalIgnoreCase))
                return "📄 Document";
            if (mediaMimeType.Equals("text/plain", StringComparison.OrdinalIgnoreCase))
                return "📝 Text";

            return "Unknown Media";
        }

        private async Task<bool> UpdateExpiredChatStatusesAsync()
        {
            try
            {
                var expirationTime = DateTime.UtcNow.AddMinutes(-1440); // 24 hours ago

                // Retrieve contacts that are open and have a LastMessageAt older than 24 hours
                var contactsToUpdate = await appDbContext.Contacts
                    .Where(c => c.ChatStatus == EngagetoEntities.Enums.ChatStatus.open &&
                                c.LastMessageAt.HasValue &&
                                c.LastMessageAt.Value <= expirationTime)
                    .ToListAsync();

                // Update the ChatStatus of each retrieved contact
                foreach (var contact in contactsToUpdate)
                {
                    contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.expired;
                }

                // Save changes to the database
                if (contactsToUpdate.Any())
                {
                    appDbContext.Contacts.UpdateRange(contactsToUpdate);
                    await appDbContext.SaveChangesAsync();
                }

                return true;
            }
            catch (Exception ex)
            {
                // Log the exception (consider using a logging framework)
                return false;
            }
        }

        public async Task<Object> GetInboxConversations(Guid businessId, string userContact)
        {
            var contact = userContact.StartsWith("+") ? userContact.Replace("+", "") : userContact;
            var data = appDbContext.Contacts.FirstOrDefault(m => m.BusinessId == businessId && m.IsActive && ((m.CountryCode + m.Contact).Replace("+", "")) == contact && (m.IsSpam == false || m.IsSpam == null));
            if (data == null)
            {
                data = new Contacts();
            }

            var name = !string.IsNullOrEmpty(data.Name) ? data.Name : userContact;
            var businessDetailsMeta = appDbContext.BusinessDetailsMetas.FirstOrDefault(m => m.BusinessId == businessId.ToString());
            if (businessDetailsMeta == null || string.IsNullOrEmpty(businessDetailsMeta.BusinessId))
                throw new Exception("Business id does not exist!");
            
            var ConversationssQuery = appDbContext.Conversations
                .Where(m => (m.From == contact && m.To == businessId.ToString()) || (m.To == contact && m.From == businessId.ToString()))
                .OrderBy(m => m.CreatedAt).ToList();
            
            if (ConversationssQuery != null)
            {

                var LastResponse = ConversationssQuery.Where(m => (m.From == contact && m.To == businessId.ToString())).OrderByDescending(m => m.CreatedAt).FirstOrDefault();
                if (LastResponse != null)
                {

                    var WhatsAppMessageId = LastResponse.WhatsAppMessageId;
                    MarkMessageRequest markMessageRequest = new MarkMessageRequest();
                    markMessageRequest.MessageId = WhatsAppMessageId;
                    markMessageRequest.Status = "read";

                    try
                    {
                        await _whatsAppBusinessClient.MarkMessageAsReadAsync(markMessageRequest, businessId);
                    }
                    catch (InvalidOperationException ex)
                    {
                        throw new Exception($"Internal Server Error: {ex.Message}");
                    }
                }
            }
            var result = new List<object>();
            foreach (var item in ConversationssQuery ?? new())
            {
                result.Add(new
                {
                    Id = item.Id,
                    WhatsAppMessageId = item.WhatsAppMessageId,
                    From = item.From,
                    To = item.To,
                    BusinessId = item.BusinessId,
                    ContactId = data.ContactId,
                    Reply = ConversationssQuery?.FirstOrDefault(m => (m.WhatsAppMessageId == item.ReplyId)),
                    Status = item.Status.ToString(),
                    CreatedAt = item.CreatedAt,
                    TextMessage = item.TextMessage,
                    MediaFileName = item.MediaFileName,
                    MediaMimeType = item.MediaMimeType,
                    MediaUrl = item.MediaUrl,
                    MediaCaption = item.MediaCaption,
                    TemplateMediaType = item.TemplateMediaType.ToString(),
                    TemplateMediaUrl = item.TemplateMediaUrl,
                    TemplateHeader = item.TemplateHeader,
                    TemplateBody = item.TemplateBody,
                    TemplateFooter = item.TemplateFooter,
                    CallButtonName = item.CallButtonName,
                    PhoneNumber = item.PhoneNumber,
                    UrlButtonNames = item.UrlButtonNames != null ? item.UrlButtonNames.Split(',').ToArray() : null,
                    RedirectUrls = item.RedirectUrls != null ? item.RedirectUrls.Split(',').ToArray() : null,
                    QuickReplies = item.QuickReplies != null ? item.QuickReplies.Split(',').ToArray() : null
                });
            }

            var Output = new
            {
                AssignTo = data.UserId,
                ContactId = data.ContactId,
                Name = name,
                Contact = contact,
                data = result
            };
            return Output;
        }

        #region Inbox Contacts
        public async Task<Object> InboxContacts(Guid BusinessId, Guid UserId, FilterDto? Operations, int? Page = 1)
        {

            int PageSize = 30;

            var UserIdExist = _applicationDBContext.UserRoles;
            List<Contacts> data = new List<Contacts>();
            string Role = "";

            if (UserIdExist.Select(m => m.Id).Contains(UserId))
            {
                var RoleId = UserIdExist.FirstOrDefault(m => m.Id == UserId).RoleId;
                var Data = _applicationDBContext.Roles.FirstOrDefault(m => m.Id == RoleId);
                if (Data != null)
                {
                    Role = Data.Name;
                }

                if (Role == "Admin" || Role == "Owner")
                {
                    data = await appDbContext.Contacts
                            .Where(m => m.BusinessId == BusinessId && m.IsActive == true).ToListAsync();
                }
                else
                {
                    data = await appDbContext.Contacts
                            .Where(m => m.BusinessId == BusinessId && m.IsActive == true && m.UserId == UserId).ToListAsync();
                }
            }

            List<InboxContactDto> Result = new List<InboxContactDto>();
            var businessId = BusinessId.ToString().ToLower();
            if (businessId != null)
            {

                var Conversations = await appDbContext.Conversations
                    .Where(m => m.From.ToLower() == businessId || m.To.ToLower() == businessId)
                    .Select(m => new { m.From, m.To, m.TextMessage, m.MediaMimeType, m.MediaFileName, m.TemplateBody, m.CreatedAt, m.Status })
                    .ToListAsync();

                foreach (var contact in data)
                {
                    var phoneNumber = contact.CountryCode + contact.Contact;
                    string phoneNumberId = phoneNumber.Replace("+", "");
                    var name = !string.IsNullOrEmpty(contact.Name) ? contact.Name : phoneNumber;

                    var lastConversations = Conversations
                                            .Where(m => (m.From == phoneNumberId && m.To.ToLower() == businessId) || (m.To == phoneNumberId && m.From.ToLower() == businessId))
                                            .OrderByDescending(m => m.CreatedAt).FirstOrDefault();

                    var lastMessage = "";
                    if (lastConversations != null)
                    {
                        if (lastConversations.TextMessage != null)
                            lastMessage += lastConversations.TextMessage;
                        else if (lastConversations.MediaMimeType != null)
                        {
                            string mediaMessage = lastConversations.MediaFileName ?? lastConversations.MediaMimeType;
                            lastMessage = lastConversations.MediaMimeType.StartsWith("image/") ? "🖼️ Photo" :
                                          lastConversations.MediaMimeType.StartsWith("video/") ? "🎥 Video" :
                                          lastConversations.MediaMimeType.StartsWith("audio/") ? "🎧 Audio" :
                                          lastConversations.MediaMimeType.StartsWith("application/") ? "📄 Document" :
                                          lastConversations.MediaMimeType == "text/plain" ? "📝 Text" : mediaMessage;

                        }
                        else if (lastConversations.TemplateBody != null)
                        {
                            lastMessage = "📝 " + lastConversations.TemplateBody;
                        }
                    }
                    var NotRead = Conversations
                                    .Where(m => m.From == phoneNumberId && m.To.ToLower() == businessId && m.Status == 0).Count();

                    if (lastConversations != null)
                    {
                        var lastMessageReceived = Conversations
                                                .Where(m => (m.From == phoneNumberId && m.To.ToLower() == businessId))
                                                .OrderByDescending(m => m.CreatedAt).FirstOrDefault();

                        if (lastMessageReceived != null)
                        {
                            if ((DateTime.UtcNow - lastMessageReceived.CreatedAt).TotalMinutes < 1440)
                            {
                                if (contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.open && contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.resolved)
                                {
                                    await _inboxService.SaveAndUpdateChatStatusAsync(contact.ContactId,
                                        EngagetoEntities.Enums.ChatStatus.open, contact.UserId ?? null);

                                    contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.open;
                                    appDbContext.Update(contact);
                                    await appDbContext.SaveChangesAsync();
                                }
                            }

                            else if ((DateTime.UtcNow - lastMessageReceived.CreatedAt).TotalMinutes >= 1440)
                            {

                                if (contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.expired)
                                {
                                    await _inboxService.SaveAndUpdateChatStatusAsync(contact.ContactId,
                                        EngagetoEntities.Enums.ChatStatus.expired, contact.UserId ?? null);

                                    contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.expired;
                                    appDbContext.Update(contact);
                                    await appDbContext.SaveChangesAsync();
                                }
                            }
                        }
                        else
                        {
                            if (contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.New)
                            {
                                await _inboxService.SaveAndUpdateChatStatusAsync(contact.ContactId,
                                EngagetoEntities.Enums.ChatStatus.New, contact.UserId ?? null);

                                contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.New;
                                appDbContext.Update(contact);
                                await appDbContext.SaveChangesAsync();
                            }
                        }
                    }
                    else
                    {
                        if (contact.ChatStatus != EngagetoEntities.Enums.ChatStatus.New)
                        {
                            await _inboxService.SaveAndUpdateChatStatusAsync(contact.ContactId,
                             EngagetoEntities.Enums.ChatStatus.New, contact.UserId ?? null);
                            contact.ChatStatus = EngagetoEntities.Enums.ChatStatus.New;
                            appDbContext.Update(contact);
                            await appDbContext.SaveChangesAsync();
                        }
                    }

                    List<Guid> ListOfUserId = appDbContext.Users.Where(m => m.Status).Select(m => m.Id).ToList();
                    if (contact.UserId != null)
                    {
                        if (!ListOfUserId.Contains((contact.UserId ?? Guid.Empty)))
                        {
                            contact.UserId = null;
                            appDbContext.Update(contact);
                            await appDbContext.SaveChangesAsync();
                        }
                    }

                    InboxContactDto result = new()
                    {
                        Contact = phoneNumberId,
                        Name = name,
                        ContactId = contact.ContactId,
                        ChatStatus = contact.ChatStatus.ToString() == EngagetoEntities.Enums.ChatStatus.New.ToString() ? "new" : contact.ChatStatus.ToString(),
                        Assignee = contact.UserId?.ToString() ?? "",
                        Tags = contact?.Tags?.Split(',')
                           .Where(s => !string.IsNullOrEmpty(s))
                           .ToArray() ?? Array.Empty<string>(),
                        LastMessageAt = lastConversations != null ? DateTime.UtcNow - lastConversations.CreatedAt : (TimeSpan?)null,
                        LastMessage = lastMessage,
                        UnRead = NotRead,
                        IsSpam = contact.IsSpam
                    };

                    Result.Add(result);
                }
            }

            Result = Result
            .OrderBy(m => m.IsSpam == true ? 1 : 0)
            .ThenBy(m => m.LastMessageAt == null ? 1 : 0)
            .ThenBy(m => m.LastMessageAt)
            .ToList();
            var data1 = new List<InboxContactDto>();
            if (Operations != null)
            {
                if (Operations.Searching != null && !string.IsNullOrEmpty(Operations.Searching.Value))
                {
                    data1 = Result.Where(m => m.Name.ToLower().Contains(Operations.Searching.Value.ToLower())).ToList();
                    if (data1.Count > 0)
                    {
                        Result = data1;
                    }
                    else
                    {
                        data1 = Result.Where(m => m.Contact.ToLower().Contains(Operations.Searching.Value.ToLower())).ToList();

                        Result = data1;


                    }
                }

                if (Operations.Sorting != null && !string.IsNullOrEmpty(Operations.Sorting.Column))
                {
                    var property = typeof(InboxContactDto).GetProperty(Operations.Sorting.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                    if (property != null)
                    {
                        Result = Operations.Sorting.Order == "desc" ?
                            Result.OrderByDescending(m => property.GetValue(m)).ToList() :
                            Result.OrderBy(m => property.GetValue(m)).ToList();
                    }
                }

                if (Operations.Filtering != null && Operations.Filtering.FilterType != null && Operations.Filtering.Conditions.Any())
                {
                    if (Operations.Filtering.FilterType == "and")
                    {
                        foreach (var condition in Operations.Filtering.Conditions)
                        {
                            var property = typeof(InboxContactDto).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                Result = ApplyCondition(Result, condition, property);
                            }
                        }
                    }
                    else if (Operations.Filtering.FilterType == "or")
                    {
                        var tempFilteredList = new List<InboxContactDto>();
                        foreach (var condition in Operations.Filtering.Conditions)
                        {
                            var property = typeof(InboxContactDto).GetProperty(condition.Column, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                            if (property != null)
                            {
                                tempFilteredList.AddRange(ApplyCondition(Result, condition, property));
                            }
                        }
                        Result = tempFilteredList.Distinct().ToList();
                    }
                }
            }

            int totalCount = Result.Count;
            int totalPages = (int)Math.Ceiling(totalCount / (double)PageSize);
            var paginatedResult = Result.Skip((Convert.ToInt32(Page) - 1) * PageSize).Take(PageSize).ToList();

            var response = new
            {
                data = paginatedResult,
                TotalCount = totalCount,
                TotalPages = totalPages,
                PageSize = PageSize,
                CurrentPage = Page
            };

            return response;



        }
        private List<InboxContactDto> ApplyCondition(List<InboxContactDto> list, FilterCondition condition, PropertyInfo property)
        {
            if (property != null)
            {
                if (property.PropertyType == typeof(string))
                {
                    switch (condition?.Operator?.ToLower())
                    {
                        case "equals":
                            list = list.Where(item =>
                            {
                                var value = property.GetValue(item)?.ToString();
                                if (condition?.Value?.ToLower() == "unassigned")
                                {
                                    return string.IsNullOrEmpty(value);
                                }
                                else
                                {
                                    return value?.ToLower() == condition?.Value?.ToLower();
                                }
                            }).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item => !property.GetValue(item).Equals(condition.Value)).ToList();
                            break;

                        case "contain":
                            list = list.Where(item => ((string)property.GetValue(item)).Contains(condition.Value)).ToList();
                            break;

                        case "startswith":
                            list = list.Where(item => ((string)property.GetValue(item)).StartsWith(condition.Value)).ToList();
                            break;

                        case "endswith":
                            list = list.Where(item => ((string)property.GetValue(item)).EndsWith(condition.Value)).ToList();
                            break;
                        default:

                            break;
                    }

                }
                if (property.PropertyType == typeof(string[]))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item =>
                                ((string[])property.GetValue(item))?.Any(val => val?.ToLower() == condition.Value.ToLower()) ?? false
                            ).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item =>
                                ((string[])property.GetValue(item))?.All(val => val != condition.Value) ?? true
                            ).ToList();
                            break;
                        case "contain":
                            list = list.Where(item =>
                                ((string[])property.GetValue(item))?.Contains(condition.Value, StringComparer.OrdinalIgnoreCase) ?? false
                            ).ToList();
                            break;
                        default:
                            break;
                    }
                }
                else if (property.PropertyType == typeof(bool?))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item => (bool)property.GetValue(item) == Convert.ToBoolean(condition.Value)).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item => (bool)property.GetValue(item) != Convert.ToBoolean(condition.Value)).ToList();
                            break;

                        default:
                            // Handle unsupported operations or raise an exception
                            break;
                    }
                }
                else if (property.PropertyType == typeof(EngagetoEntities.Enums.ChatStatus))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item => (int)property.GetValue(item) == Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item => (int)property.GetValue(item) != Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "greaterthan":
                            list = list.Where(item => (int)property.GetValue(item) > Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "lessthan":
                            list = list.Where(item => (int)property.GetValue(item) < Convert.ToInt32(condition.Value)).ToList();
                            break;

                        default:
                            // Handle unsupported operations or raise an exception
                            break;
                    }
                }
                else if (property.PropertyType == typeof(int))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item => (int)property.GetValue(item) == Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item => (int)property.GetValue(item) != Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "greaterthan":
                            list = list.Where(item => (int)property.GetValue(item) > Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "lessthan":
                            list = list.Where(item => (int)property.GetValue(item) < Convert.ToInt32(condition.Value)).ToList();
                            break;

                        default:
                            // Handle unsupported operations or raise an exception
                            break;
                    }
                }
                else if (property.PropertyType == typeof(int?))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":
                            list = list.Where(item => (int)property.GetValue(item) == Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "notequals":
                            list = list.Where(item => (int)property.GetValue(item) != Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "greaterthan":
                            list = list.Where(item => (int)property.GetValue(item) > Convert.ToInt32(condition.Value)).ToList();
                            break;

                        case "lessthan":
                            list = list.Where(item => (int)property.GetValue(item) < Convert.ToInt32(condition.Value)).ToList();
                            break;

                        default:
                            // Handle unsupported operations or raise an exception
                            break;
                    }
                }


                // ... (Previous code remains the same)



                else if (property.PropertyType == typeof(DateTime?))
                {
                    if (DateTime.TryParseExact(condition.Value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dateTimeValue))
                    {
                        switch (condition.Operator.ToLower())
                        {
                            case "equals":
                                list = list.Where(item => ((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "notequals":
                                list = list.Where(item => !((DateTime?)property.GetValue(item)).Equals(dateTimeValue)).ToList();
                                break;

                            case "after":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) > 0).ToList();
                                break;

                            case "before":
                                list = list.Where(item => ((DateTime?)property.GetValue(item))?.CompareTo(dateTimeValue) < 0).ToList();
                                break;


                            // Add more date-based operators as needed

                            default:
                                // Handle unsupported operator
                                break;
                        }
                    }
                }
                else if (property.PropertyType == typeof(Guid?))
                {
                    switch (condition.Operator.ToLower())
                    {
                        case "equals":

                            list = list.Where(item => ((string)property.GetValue(item)).Contains(condition.Value)).ToList();
                            break;
                        case "notequals":
                            list = list.Where(item => !((string)property.GetValue(item)).Contains(condition.Value)).ToList();
                            break;
                        default:
                            break;
                    }
                }

            }
            return list;

        }
        #endregion
        #region Inbox Conversations

        public async Task<object> FetchInboxConversations(Guid BusinessId, Guid contactId, Guid userId, int? Page = 1)
        {
            var data = appDbContext.Contacts.Where(m => m.IsActive && m.ContactId == contactId).FirstOrDefault();
            if (data == null)
            {
                data = new Contacts();
            }
            var name = !string.IsNullOrEmpty(data.Name) ? data.Name : (data.CountryCode+data.Contact);

            // Ensure the current page is set to 1 if it is not provided or invalid
            int currentPage = Math.Max(1, Convert.ToInt32(Page));
            // Parameters for pagination
            int itemsPerPage = 50;
            var convs = await appDbContext.Conversations
               .Where(m => (m.BusinessId == BusinessId && m.ContactId == contactId))
               .OrderByDescending(m => m.CreatedAt).Skip(itemsPerPage * (currentPage - 1)).Take(itemsPerPage).ToListAsync();

            //Update as read message which received from customer
            var receivedCustomerMessage = convs.Where(x => x.Status == ConvStatus.received).ToList();
            receivedCustomerMessage.ForEach(x => x.Status = ConvStatus.receivedRead);
            appDbContext.Conversations.UpdateRange(receivedCustomerMessage);
            await appDbContext.SaveChangesAsync();

            int totalItems = appDbContext.Conversations
               .Count(m => m.ContactId == contactId && m.BusinessId == BusinessId);

            if (Page == 1)
            {
                var LastResponse = convs.Where(m => m.Status == ConvStatus.received).OrderByDescending(m => m.CreatedAt).FirstOrDefault();
                if (LastResponse != null)
                {
                    var WhatsAppMessageId = LastResponse.WhatsAppMessageId;
                    MarkMessageRequest markMessageRequest = new MarkMessageRequest
                    {
                        MessageId = WhatsAppMessageId,
                        Status = "read"
                    };
                    await _whatsAppBusinessClient.MarkMessageAsReadAsync(markMessageRequest, BusinessId);
                }
            }
            // Calculate the total number of pages
            int totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);

            // Ensure the current page is within the valid range
            currentPage = Math.Max(1, Math.Min(currentPage, totalPages));

            EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();

            // Transform the result into the desired format
            var replies = appDbContext.Conversations.Where(m => convs.Select(c => c.ReplyId).Contains(m.WhatsAppMessageId)).ToList();
            var result = convs.Select(conv => messages.Message(conv, replies)).ToList();

            // Prepare the output
            var output = new
            {
                AssignTo = data.UserId,
                ContactId = data.ContactId,
                Name = name,
                Contact = (data.CountryCode+data.Contact).Replace("+",""),
                TotalItems = totalItems,
                TotalPages = totalPages,
                CurrentPage = currentPage,
                ItemsPerPage = itemsPerPage,
                Data = result.OrderBy(x => x.CreatedAt),
                IsSpam = data.IsSpam,
                data?.IsOptIn,
                ChatStatus = data.ChatStatus.ToString()
            };
            // Return the output
            return output;
        }

        public async Task<object> InboxContactsConversations(Guid BusinessId, string Contact, int? Page)
        {
            var data = appDbContext.Contacts.Where(m => m.IsActive).FirstOrDefault(m => m.BusinessId == BusinessId && m.IsActive && ((m.CountryCode + m.Contact).Replace("+", "")) == Contact);
            if (data == null)
            {
                data = new Contacts();
            }

            var name = !string.IsNullOrEmpty(data.Name) ? data.Name : Contact;

            // Ensure the current page is set to 1 if it is not provided or invalid
            int currentPage = Math.Max(1, Convert.ToInt32(Page));

            var businessId = BusinessId.ToString().ToLower();

            // Parameters for pagination
            int itemsPerPage = 50;

            // Query to get the items for the current page
            var convs = await appDbContext.Conversations
                .Where(m => (m.From.Replace("+", "") == Contact.ToLower() && m.To.ToLower() == businessId) || (m.To.Replace("+", "") == Contact && m.From.ToLower() == businessId))
                .OrderByDescending(m => m.CreatedAt).Skip(itemsPerPage * (currentPage - 1)).Take(itemsPerPage).ToListAsync();

            //Update as read message which received from customer
            var receivedCustomerMessage = convs.Where(x => x.From == Contact && x.To == businessId).ToList();

            receivedCustomerMessage.ForEach(x =>
            {
                if (x.Status == ConvStatus.received || x.Status == ConvStatus.receivedRead)
                {
                    x.Status = ConvStatus.receivedRead;
                }
            });

            //receivedCustomerMessage.ForEach(x => x.Status = ConvStatus.receivedRead);
            appDbContext.Conversations.UpdateRange(receivedCustomerMessage);
            await appDbContext.SaveChangesAsync();

            int totalItems = appDbContext.Conversations
               .Count(m => (m.From == Contact.ToLower() && m.To.ToLower() == businessId) || (m.To == Contact && m.From.ToLower() == businessId));

            if (Page == 1)
            {
                var LastResponse = convs.Where(m => (m.From == Contact && m.To.ToLower() == businessId)).OrderByDescending(m => m.CreatedAt).FirstOrDefault();
                if (LastResponse != null)
                {
                    var WhatsAppMessageId = LastResponse.WhatsAppMessageId;
                    MarkMessageRequest markMessageRequest = new MarkMessageRequest
                    {
                        MessageId = WhatsAppMessageId,
                        Status = "read"
                    };
                    await _whatsAppBusinessClient.MarkMessageAsReadAsync(markMessageRequest, BusinessId);
                }
            }
            // Calculate the total number of pages
            int totalPages = (int)Math.Ceiling((double)totalItems / itemsPerPage);

            // Ensure the current page is within the valid range
            currentPage = Math.Max(1, Math.Min(currentPage, totalPages));

            EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();

            // Transform the result into the desired format
            var replies = appDbContext.Conversations.Where(m => convs.Select(c => c.ReplyId).Contains(m.WhatsAppMessageId)).ToList();
            var result = convs.Select(conv => messages.Message(conv, replies)).ToList();

            // Prepare the output
            var output = new
            {
                AssignTo = data.UserId,
                ContactId = data.ContactId,
                Name = name,
                Contact = Contact,
                TotalItems = totalItems,
                TotalPages = totalPages,
                CurrentPage = currentPage,
                ItemsPerPage = itemsPerPage,
                Data = result.OrderBy(x => x.CreatedAt),
                IsSpam = data.IsSpam,
                data?.IsOptIn,
                ChatStatus = data.ChatStatus.ToString()
            };
            // Return the output
            return output;
        }
        #endregion
        #region Send Text Message
        public async Task SendTextOrEmojiMessage(TextMessageDto data, Guid BusinessId, string? SentBy = null)
        {
            var results = new List<object>();

            var result = new object();
            foreach (var Contact in data.Contact)
            {
                if (data.TextMessage != null && data.TextMessage != "")
                {
                    TextMessageRequest textMessageRequest = new TextMessageRequest();
                    textMessageRequest.To = Contact;
                    textMessageRequest.Text = new WhatsAppText();
                    textMessageRequest.Text.Body = data.TextMessage;
                    textMessageRequest.Text.PreviewUrl = false;
                    if (data.MessageId != null && data.MessageId != "")
                    {
                        textMessageRequest.Context = new();
                        textMessageRequest.Context.MessageId = data.MessageId;
                    }
                    result = await _whatsAppBusinessClient.SendMessageAsync(textMessageRequest, BusinessId, SentBy);
                }

                results.Add(result);
            }




        }
        #endregion
        #region Send Media Message
        public async Task SendMediaMessage(MediaMessageDto data, Guid BusinessId, string? SentBy = null)
        {
            var results = new List<object>();

            var result = new object();


            foreach (var Contact in data.Contact)
            {


                if (data.MediaFile != null)
                {


                    var client = new HttpClient();
                    var response = await client.GetAsync(data.MediaFile);

                    if (response.IsSuccessStatusCode)
                    {
                        var contentType = response.Content.Headers.ContentType?.MediaType;
                        Uri uri = new Uri(data.MediaFile);
                        Path.GetFileName(uri.LocalPath);
                        var fileName = Path.GetFileName(uri.LocalPath);

                        if (contentType != null)
                        {
                            // Check media type based on content type
                            if (contentType.StartsWith("image"))
                            {
                                ImageMessageRequest imageMessageRequest = new ImageMessageRequest();
                                imageMessageRequest.To = Contact;
                                imageMessageRequest.Image = new WhatsAppImage();
                                imageMessageRequest.Image.Link = data.MediaFile;
                                imageMessageRequest.Image.Caption = data.Caption;
                                if (data.MessageId != null && data.MessageId != "")
                                {
                                    imageMessageRequest.Context = new EngagetoEntities.Dtos.MetaDto.MessageContext();
                                    imageMessageRequest.Context.MessageId = data.MessageId;
                                }
                                result = await _whatsAppBusinessClient.SendMessageAsync(imageMessageRequest, BusinessId, SentBy);
                                // Image file
                                // Handle image processing or save to local storage
                            }
                            else if (contentType.StartsWith("video"))
                            {
                                VideoMessageRequest videoMessageRequest = new VideoMessageRequest();
                                videoMessageRequest.To = Contact;
                                videoMessageRequest.Video = new WhatsAppVideo();
                                videoMessageRequest.Video.Link = data.MediaFile;
                                videoMessageRequest.Video.Caption = data.Caption;

                                if (data.MessageId != null && data.MessageId != "")
                                {
                                    videoMessageRequest.Context = new EngagetoEntities.Dtos.MetaDto.MessageContext();
                                    videoMessageRequest.Context.MessageId = data.MessageId;
                                }
                                result = await _whatsAppBusinessClient.SendMessageAsync(videoMessageRequest, BusinessId, SentBy);
                                // Video file
                                // Handle video processing or save to local storage
                            }
                            else if (contentType.StartsWith("audio"))
                            {
                                AudioMessageRequest audioMessageRequest = new AudioMessageRequest();
                                audioMessageRequest.To = Contact;
                                audioMessageRequest.Audio = new WhatsAppAudio();
                                audioMessageRequest.Audio.Link = data.MediaFile;


                                if (data.MessageId != null && data.MessageId != "")
                                {
                                    audioMessageRequest.Context = new EngagetoEntities.Dtos.MetaDto.MessageContext();
                                    audioMessageRequest.Context.MessageId = data.MessageId;
                                }
                                result = await _whatsAppBusinessClient.SendMessageAsync(audioMessageRequest, BusinessId, SentBy);
                                // Audio file
                                // Handle audio processing or save to local storage
                            }
                            else if (contentType.StartsWith("text/plain") ||
                                contentType.StartsWith("application/pdf") ||
                                contentType.StartsWith("application/vnd.ms-powerpoint") ||
                                contentType.StartsWith("application/msword") ||
                                contentType.StartsWith("application/vnd.ms-excel") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.presentationml.presentation") ||
                                contentType.StartsWith("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                            {
                                DocumentMessageRequest documentMessageRequest = new DocumentMessageRequest();
                                documentMessageRequest.To = Contact;
                                documentMessageRequest.Document = new WhatsAppDocument();
                                documentMessageRequest.Document.Link = data.MediaFile;
                                documentMessageRequest.Document.Caption = data.Caption;
                                documentMessageRequest.Document.FileName = data.MediaFileName;

                                if (data.MessageId != null && data.MessageId != "")
                                {
                                    documentMessageRequest.Context = new();
                                    documentMessageRequest.Context.MessageId = data.MessageId;
                                }
                                result = await _whatsAppBusinessClient.SendMessageAsync(documentMessageRequest, BusinessId, SentBy);

                            }

                        }
                    }



                }

                results.Add(result);
            }




        }
        #endregion
        #region Send Template Message
        public async Task SendTemplateByUsingContact(SendTemplate model, string? SentBy = null)
        {
            try
            {

                bool isValidFile = true;
                if (model.MediaFile != null)
                {
                    var client = _httpClientFactory.CreateClient();
                    var res = await client.GetAsync(model.MediaFile);
                    if (!res.IsSuccessStatusCode)
                    {

                    }

                    var contentType = res.Content.Headers.ContentType?.MediaType;
                    var fileSize = res.Content.Headers.ContentLength ?? 0;
                    var fileName = model.MediaFile.Split('/').Last();
                    var fileType = contentType;

                    byte[] fileBytes = await res.Content.ReadAsByteArrayAsync();
                    isValidFile = false;

                    if (fileSize > 0)
                    {
                        if (fileSize <= 5242880 && (fileType == "image/jpeg" || fileType == "image/png")) // 5MB for images
                        {
                            isValidFile = true;
                        }
                        else if (fileSize <= 104857600 && (fileType.StartsWith("text/") || fileType == "application/pdf" || fileType.StartsWith("application/vnd"))) // 100MB for documents
                        {
                            isValidFile = true;
                        }
                        else if (fileType.StartsWith("video/"))
                        {
                            // Supported video types and size check
                            var supportedVideoTypes = new[] { "video/mp4", "video/3gp" };
                            var maxVideoFileSize = 16 * 1024 * 1024; // 16MB

                            if (supportedVideoTypes.Contains(fileType) && fileSize <= maxVideoFileSize)
                            {
                                isValidFile = true;
                            }
                        }
                    }

                }
                if (isValidFile)
                {
                    // Call the service to send the template with media
                    var response = await SendTemplateAsync(model, SentBy);
                }
            }
            catch (Exception ex)
            {
            }
        }

        public async Task<HttpResponseMessage> SendTemplateAsync(SendTemplate model, string? SentBy = null)
        {

            var Business = appDbContext.BusinessDetailsMetas.FirstOrDefault(t => t.BusinessId == model.BusinessId);
            var user = appDbContext.Users.FirstOrDefault(u => u.Id == model.UserId);

            if (Business == null || user == null)
            {
                throw new Exception("Business or User with the specified ID does not exist.");
            }

            if (string.IsNullOrEmpty(Business.BusinessId) || user.Id == Guid.Empty)
            {
                throw new Exception("BusinessId or UserId is invalid.");
            }

            if (!string.Equals(user.CompanyId, model.BusinessId, StringComparison.OrdinalIgnoreCase))
            {
                throw new Exception("User does not have access to the specified business.");
            }
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer " + Business.Token);

            HttpResponseMessage response = null;

            var regex = StringHelper.GetVariableRegexs();
            foreach (var phoneNumberId in model.Contact)
            {
                // Find the contact with the given ID and retrieve Contact and CountryCode properties

                if (phoneNumberId != null)
                {
                    string To = phoneNumberId;
                    var jsonData = "";
                    var template = await appDbContext.Templates.FirstOrDefaultAsync(t => t.TemplateId == model.TemplateId);
                    if (template == null) throw new Exception("Template not found!");
                    template.Buttons = await appDbContext.ButtonDetails.Where(m => m.TemplateId == template.TemplateId).ToListAsync();
                    // Separate buttons by type
                    var urlButtons = template.Buttons.Where(b => b.ButtonType == "URL").ToList();
                    var phoneNumberButtons = template.Buttons.FirstOrDefault(b => b.ButtonType == "PHONE_NUMBER");
                    var quickReplyButtons = template.Buttons.Where(b => b.ButtonType == "QUICK_REPLY").ToList();
                    string TemptButtonRedirectUrls = "";
                    string TemptBody = "";
                    string TempHeader = "";
                    var bodyMessage = StringHelper.ReplaceAndExtractVariables(template.Body).UpdatedMessage;
                    TemptBody = bodyMessage;
                    if (template != null && TemptBody != null && regex.IsMatch(TemptBody))
                    {
                        var replacedBody = bodyMessage;
                        var matches = Regex.Matches(replacedBody, @"\{\{(\d+)\}\}");
                        int index = matches.Count();
                        int a = 0;
                        foreach (Match match in matches)
                        {
                            if (index > 0 && index <= model.BodyVariableValues?.Length)
                            {
                                var bodyValueParts = model.BodyVariableValues[a].Split(',');
                                if (index > 0)
                                {
                                    replacedBody = replacedBody.Replace(match.Value, bodyValueParts[0].Trim());
                                }
                                a++;
                            }
                            index--;
                        }
                        TemptBody = replacedBody;
                    }

                    // Do something with 'fullContact' here
                    if (!string.IsNullOrEmpty(phoneNumberId))
                    {
                        if (template?.MediaType != MediaType.CAROUSEL)
                        {

                            if (template == null)
                            {
                                return new HttpResponseMessage(HttpStatusCode.NotFound);
                            }
                            var headerMessage = string.Empty;
                            if (!string.IsNullOrEmpty(template.Header))
                            {
                                headerMessage = StringHelper.ReplaceAndExtractVariables(template.Header).UpdatedMessage;
                            }
                            var MadiaUrls = template.MediaAwsUrl;

                            if (model.MadiaUrl != null || model.MediaFile != null)
                            {
                                MadiaUrls = model.MadiaUrl != null ? model.MadiaUrl : Handler.S3Url;
                                if (model.MediaFile != null)
                                {
                                    var Handler = SendTemplateS3(model);
                                    MadiaUrls = Handler.S3Url;
                                }
                            }

                            //for save into database
                            TempHeader = headerMessage;
                            string TemptButtonQuickReply = String.Join(",", urlButtons.Select(m => m.ButtonName));
                            string TemptButtonRedirectUrl = string.Join(",", urlButtons.Select(b => b.ButtonValue));

                            int i = 1, j = 0;
                            foreach (var button in urlButtons)
                            {
                                if (i == 1)
                                {
                                    if (regex.IsMatch(button.ButtonValue ?? string.Empty))
                                    {
                                        TemptButtonRedirectUrls = button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j]);
                                        j++;
                                    }
                                    else
                                    {
                                        TemptButtonRedirectUrls = button.ButtonValue ?? string.Empty;
                                    }
                                    i++;
                                    continue;
                                }
                                if (i == 2)
                                {
                                    TemptButtonRedirectUrls = TemptButtonRedirectUrls + ",";

                                    if (regex.IsMatch(button.ButtonValue ?? string.Empty))
                                    {
                                        TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue.Replace("{{1}}", model.RedirectUrlVariableValues[j]);
                                    }
                                    else
                                    {
                                        TemptButtonRedirectUrls = TemptButtonRedirectUrls + button.ButtonValue;
                                    }
                                }
                            }

                            if (template != null && TempHeader != null && regex.IsMatch(headerMessage))
                            {
                                if (model.HeaderVariableValue != null)
                                {
                                    TempHeader = StringHelper.ReplaceVariable(headerMessage, new string[] { model.HeaderVariableValue });
                                }
                            }

                            // send to meta json data
                            string modifiedHeader = headerMessage;
                            string modifiedBody = bodyMessage;
                            string modifiedButtonsRedirectUrl = string.Join(",", urlButtons.Select(b => b.ButtonValue));

                            if (template != null && modifiedHeader != null && regex.IsMatch(headerMessage))
                            {
                                var match = Regex.Match(headerMessage, @"\{\{\d+\}\}"); // Find the first match
                                if (match.Success)
                                {
                                    modifiedHeader = model.HeaderVariableValue ?? string.Empty;
                                }
                            }
                            //  https://www.google.com/a,https://www.google.com/b{{1}}

                            int i1 = 1, j1 = 0;
                            string modifiedButtonRedirectUrl1 = null;
                            string modifiedButtonRedirectUrl2 = null;
                            foreach (var button in urlButtons)
                            {
                                if (i1 == 1)
                                {
                                    if (regex.IsMatch(button.ButtonValue ?? string.Empty))
                                    {
                                        modifiedButtonRedirectUrl1 = model.RedirectUrlVariableValues[j1];
                                        j1++;
                                    }
                                    else
                                    {

                                    }
                                    i1++;
                                    continue;
                                }
                                if (i1 == 2)
                                {
                                    if (regex.IsMatch(button.ButtonValue ?? string.Empty))
                                    {
                                        modifiedButtonRedirectUrl2 = model.RedirectUrlVariableValues[j1];
                                    }
                                    else
                                    {

                                    }
                                }
                            }
                            if (modifiedButtonRedirectUrl1 == null)
                            {
                                modifiedButtonRedirectUrl1 = modifiedButtonRedirectUrl2 ?? string.Empty;
                                modifiedButtonRedirectUrl2 = null;
                            }
                            if (template != null && modifiedBody != null && regex.IsMatch(bodyMessage))
                            {
                                var parameters = new List<JObject>();
                                var matches = Regex.Matches(bodyMessage, @"\{\{\d+\}\}");
                                int index = matches.Count;
                                int a = 0;

                                foreach (Match match in matches)
                                {
                                    if (index > 0 && index <= model.BodyVariableValues.Length)
                                    {
                                        var bodyValueParts = model.BodyVariableValues[a].Split(',');
                                        var value = bodyValueParts[0].Trim();
                                        var parameter = new JObject
                                        {
                                            ["type"] = "text",
                                            ["text"] = value
                                        };
                                        parameters.Add(parameter);
                                        a++;
                                    }
                                    index--;
                                }
                                modifiedBody = JsonConvert.SerializeObject(parameters);
                            }

                            var Mediatype = template?.MediaType.ToString().ToLower();

                            if (Mediatype == "none" || Mediatype == "text")
                            {
                                Mediatype = null;
                            }
                            jsonData = $@"

                           {{
                                 ""messaging_product"": ""whatsapp"",
                                 ""recipient_type"": ""individual"",
                                 ""to"": ""{phoneNumberId}"",
                                 ""type"": ""template"",
                                 ""template"": {{
                                 ""name"": ""{template.TemplateName}"",
                                 ""language"": {{
                                 ""code"": ""{template.LanguageCode}""
                           }}";

                            // Check if MediaTypes is null (work in none && text template) where template,body are not null or empty, and they doesn't contain variables.
                            if (Mediatype == null && template != null && (!string.IsNullOrEmpty(bodyMessage) ||
                                                                                  !string.IsNullOrEmpty(headerMessage) ||
                                                                                          !string.IsNullOrEmpty(modifiedButtonsRedirectUrl)))
                            {
                                bool bodyContainsVariable = regex.IsMatch(bodyMessage);
                                bool headerContainsVariable = headerMessage != null ? regex.IsMatch(headerMessage) : false;
                                bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? regex.IsMatch(modifiedButtonsRedirectUrl) : false;

                                if (!bodyContainsVariable && !RedirectUrlsContainsVariable && !headerContainsVariable)
                                {
                                    jsonData += $@"

                                     }}
                                  }}";

                                }
                            }

                            // Check if MediaTypes is not null (work in media template) where template,body are not null or empty, and they doesn't contain variables.
                            if (Mediatype != null && template != null && (!string.IsNullOrEmpty(bodyMessage) || !string.IsNullOrEmpty(modifiedButtonsRedirectUrl)))
                            {
                                bool bodyContainsVariable = regex.IsMatch(bodyMessage);
                                bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? regex.IsMatch(modifiedButtonsRedirectUrl) : false;

                                if (!bodyContainsVariable && !RedirectUrlsContainsVariable)
                                {
                                    jsonData += $@",
                                       ""components"": [
                                                        {{
                                                            ""type"": ""header"",
                                                                      ""parameters"": [
                                                                                       {{
                                                                                         ""type"": ""{Mediatype.ToLower()}"",
                                                                                         ""{Mediatype.ToLower()}"": {{
                                                                                                                      ""link"": ""{MadiaUrls}""
                                                                                                                     }}
                                                                                       }}
                                                                                      ]
                                                                                    }}
                                                        ]
                                                     }} 
                                                }}";
                                }
                            }
                            // Check if MediaTypes is null (work in none template) where template,body are not null or empty, and they contain variables.

                            if (Mediatype == null && template != null && ((!string.IsNullOrEmpty(bodyMessage) && regex.IsMatch(bodyMessage))
                                                                            || (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl)
                                                                                && regex.IsMatch(modifiedButtonsRedirectUrl)))
                                                                                   && string.IsNullOrEmpty(headerMessage))
                            {
                                bool bodyContainsVariable = regex.IsMatch(bodyMessage);
                                bool RedirectUrlsContainsVariable = modifiedButtonsRedirectUrl != null ? regex.IsMatch(modifiedButtonsRedirectUrl) : false;

                                jsonData += $@",
                                   ""components"": [";
                                bool firstComponent = true;

                                if (bodyContainsVariable)
                                {
                                    jsonData += $@"
                                                {{
                                                  ""type"": ""body"",
                                                  ""parameters"": {modifiedBody}
                                                }}";

                                    firstComponent = false;
                                }
                                int index = 0;
                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }
                                if (RedirectUrlsContainsVariable)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }
                                        jsonData += $@"     
                                                     {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl1}""
                                                           }}
                                                         ]
                                                      }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                      {{
                                                          ""type"": ""button"",
                                                          ""sub_type"": ""url"",
                                                          ""index"": ""{index}"",
                                                          ""parameters"": [
                                                           {{
                                                              ""type"": ""PAYLOAD"",
                                                              ""payload"":""{modifiedButtonRedirectUrl2}""
                                                           }}
                                                         ]
                                                      }}";
                                        }
                                    }
                                }
                                jsonData += $@"
                                     ]
                                   }}
                                }}";
                            }

                            // Check if MediaTypes is null (work in text template) and template, body, and header are not null or empty, and they contain variables.
                            if (Mediatype == null && template != null && (((!string.IsNullOrEmpty(bodyMessage) && regex.IsMatch(bodyMessage)) ||
                                                                            (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl) && regex.IsMatch(modifiedButtonsRedirectUrl)) ||
                                                                              (!string.IsNullOrEmpty(headerMessage) && regex.IsMatch(headerMessage)))) && !string.IsNullOrEmpty(headerMessage))

                            {
                                bool bodyContainsVariable1 = regex.IsMatch(bodyMessage);
                                bool headerContainsVariable1 = headerMessage != null ? regex.IsMatch(headerMessage) : false;
                                bool RedirectUrlsContainsVariable1 = modifiedButtonsRedirectUrl != null ? regex.IsMatch(modifiedButtonsRedirectUrl) : false;

                                jsonData += $@",
                                   ""components"": [";

                                bool firstComponent = true;
                                if (headerContainsVariable1)
                                {
                                    if (!string.IsNullOrEmpty(modifiedHeader) && modifiedHeader.Trim() != "")
                                    {
                                        jsonData += $@"
                                                         {{
                                                           ""type"": ""header"",
                                                           ""parameters"": [
                                                           {{
                                                             ""type"": ""text"",
                                                             ""text"": ""{modifiedHeader}""
                                                           }}
                                                         ]
                                                    }}";

                                        firstComponent = false;
                                    }
                                }
                                if (bodyContainsVariable1)
                                {
                                    if (!firstComponent)
                                    {
                                        jsonData += ",";
                                    }
                                    jsonData += $@"
                                                        {{
                                                          ""type"": ""body"",
                                                          ""parameters"": {modifiedBody}
                                                        }}";

                                    firstComponent = false;

                                }
                                int index = 0;
                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }
                                if (RedirectUrlsContainsVariable1)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }
                                        jsonData += $@"    
                                                             {{
                                                                  ""type"": ""button"",
                                                                  ""sub_type"": ""url"",
                                                                  ""index"": ""{index}"",
                                                                  ""parameters"": [
                                                                   {{
                                                                      ""type"": ""PAYLOAD"",
                                                                      ""payload"":""{modifiedButtonRedirectUrl1}""
                                                                   }}
                                                                 ]
                                                              }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                              {{
                                                                  ""type"": ""button"",
                                                                  ""sub_type"": ""url"",
                                                                  ""index"": ""{index}"",
                                                                  ""parameters"": [
                                                                   {{
                                                                      ""type"": ""PAYLOAD"",
                                                                      ""payload"":""{modifiedButtonRedirectUrl2}""
                                                                   }}
                                                                 ]
                                                              }}";

                                        }
                                    }
                                }
                                jsonData += $@"

                               ]
                            }}
                         }}";

                            }

                            // Check if MediaTypes is not null (work in Media template) and template, body, and header are not null or empty, and they contain variables.
                            if (Mediatype != null && template != null && ((!string.IsNullOrEmpty(bodyMessage) && regex.IsMatch(bodyMessage)) ||
                                                                            (!string.IsNullOrEmpty(modifiedButtonsRedirectUrl) && regex.IsMatch(modifiedButtonsRedirectUrl))))

                            {
                                bool bodyContainsVariable2 = regex.IsMatch(bodyMessage);
                                bool RedirectUrlsContainsVariable2 = modifiedButtonsRedirectUrl != null ? regex.IsMatch(modifiedButtonsRedirectUrl) : false;
                                bool firstComponent = true;
                                jsonData += $@",
                                      ""components"": [
                                                      {{
                                                        ""type"": ""header"",
                                                                  ""parameters"": [
                                                                                  {{
                                                                                   ""type"": ""{Mediatype.ToLower()}"",
                                                                                   ""{Mediatype.ToLower()}"": {{
                                                                                                                ""link"": ""{MadiaUrls}""
                                                                                                               }}
                                                                                   }}
                                                                                  ]
                                                                              }}";


                                if (bodyContainsVariable2)
                                {
                                    jsonData += $@",
                                                {{
                                                  ""type"": ""body"",
                                                  ""parameters"": {modifiedBody}
                                                }}";

                                    firstComponent = false;
                                }

                                int index = 0;

                                if (phoneNumberButtons != null)
                                {
                                    index = 1;
                                }

                                if (RedirectUrlsContainsVariable2)
                                {
                                    if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl1) && modifiedButtonRedirectUrl1.Trim() != "")
                                    {
                                        if (!firstComponent)
                                        {
                                            jsonData += ",";
                                        }
                                        jsonData += $@"    
                                                         {{
                                                              ""type"": ""button"",
                                                              ""sub_type"": ""url"",
                                                              ""index"": ""{index}"",
                                                              ""parameters"": [
                                                               {{
                                                                  ""type"": ""PAYLOAD"",
                                                                  ""payload"":""{modifiedButtonRedirectUrl1}""
                                                               }}
                                                             ]
                                                          }}";
                                        index++;

                                        if (!string.IsNullOrEmpty(modifiedButtonRedirectUrl2) && modifiedButtonRedirectUrl2.Trim() != "")
                                        {
                                            jsonData += $@",     
                                                          {{
                                                              ""type"": ""button"",
                                                              ""sub_type"": ""url"",
                                                              ""index"": ""{index}"",
                                                              ""parameters"": [
                                                               {{
                                                                  ""type"": ""PAYLOAD"",
                                                                  ""payload"":""{modifiedButtonRedirectUrl2}""
                                                               }}
                                                             ]
                                                          }}";

                                        }
                                    }
                                }

                                jsonData += $@"
                                       ]
                                    }}
                                }}";
                            }

                        }
                        List<CarouselCardsDto> jsonCardsConverted = new List<CarouselCardsDto>();
                        if (template != null && template.MediaType == MediaType.CAROUSEL)
                        {
                            string existingBody = "";
                            var body = StringHelper.FormateTemplateComponents(template.Body);
                            var bodyReplaced = StringHelper.ReplaceAndExtractVariables(body).UpdatedMessage;
                            if (template.Body != null && StringHelper.GetVariableRegexs().IsMatch(bodyReplaced))
                            {
                                if (model.BodyVariableValues == null || model.BodyVariableValues.Length != Regex.Matches(bodyReplaced, @"\{\{\d+\}\}").Count)
                                {
                                    throw new Exception("BodyVariableValues do not match the template variables.");
                                }
                                existingBody = template.Body;
                            }

                            if (template == null)
                            {
                                return new HttpResponseMessage(HttpStatusCode.NotFound);
                            }

                            var firstCarousel = model?.CarouselVariables?.FirstOrDefault(c => !string.IsNullOrEmpty(c.MediaUrl));
                            string HeaderHandle = null;
                            if (!string.IsNullOrEmpty(existingBody))
                            {
                                var formateBody = StringHelper.FormateTemplateComponents(existingBody);
                                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                                if (!regex.IsMatch(leadratVarBody))
                                {
                                    model.BodyVariableValues = null;
                                }
                            }
                            jsonCardsConverted = JsonCardsConverter(template.CarouselCardsJson ?? string.Empty, model?.CarouselVariables);

                            var templateBody = "";
                            if (model.BodyVariableValues != null && model.BodyVariableValues.Length > 0)
                            {
                                var formateTemplateBody = StringHelper.FormateTemplateComponents(template.Body ?? string.Empty);
                                var leadratBody = StringHelper.ReplaceAndExtractVariables(formateTemplateBody).UpdatedMessage;
                                templateBody = StringHelper.ReplacePlaceholders(leadratBody, model.BodyVariableValues.ToList());
                            }
                            else
                            {
                                model.BodyVariableValues = model.BodyVariableValues;
                            }

                            TempHeader = template.Header ?? string.Empty;
                            TempHeader = templateBody;
                            string TemptButtonQuickReply = String.Join(",", urlButtons.Select(m => m.ButtonName));
                            string TemptButtonRedirectUrl = String.Join(",", urlButtons.Select(b => b.ButtonValue));
                            var TempCarouselVariables = model.CarouselVariables;

                            var leadratVariableUrl = StringHelper.ReplaceAndExtractVariables(TemptButtonRedirectUrl).UpdatedMessage;
                            if (TemptButtonRedirectUrl != null && regex.IsMatch(leadratVariableUrl))
                            {
                                if (model.CarouselVariables.Any(variables => variables.RedirectUrlVariableValues == null))
                                {
                                    throw new Exception("RedirectUrlVariableValues is required");
                                }
                            }


                            var carouselBody = "";
                            foreach (var card in jsonCardsConverted)
                            {
                                var formateCardBody = StringHelper.FormateTemplateComponents(card.Body);
                                var leadratCardVarBody = StringHelper.ReplaceAndExtractVariables(formateCardBody).UpdatedMessage;
                                if (regex.IsMatch(leadratCardVarBody))
                                {
                                    carouselBody = string.Join(",", model.CarouselVariables.Select(card => string.Join(",", card.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}"))));
                                }
                            }
                            var carouselCardsPayload = SendCarouselCardsPayload(jsonCardsConverted, template.Header ?? string.Empty, HeaderHandle ?? string.Empty, template, model.CarouselVariables);
                            jsonData = CreateTemplatePayloads.SendCarouselTemplatePayload(template.TemplateName, phoneNumberId, model?.BodyVariableValues?.ToList() ?? new(), template.LanguageCode, carouselCardsPayload, carouselBody);

                        }

                        try
                        {
                            string conversationCarouselCards = UpdatedCarouselCardsJson(jsonCardsConverted, model.CarouselVariables ?? new());
                            // Save data to Templates table
                            var contact = await appDbContext.Contacts.FirstOrDefaultAsync(x => string.Concat(x.CountryCode, x.Contact).Replace("+", "") == To.Replace("+", "") && x.BusinessId.ToString() == model.BusinessId);
                            Conversations sendTemplateEntity = new Conversations
                            {
                                TemplateMediaType = template.MediaType,
                                TemplateMediaUrl = template.MediaAwsUrl,
                                CreatedAt = DateTime.UtcNow,
                                TemplateBody = TemptBody?.Replace("\\\"", "\"").Replace("\\n", "\n"),
                                TemplateHeader = template.MediaType != MediaType.CAROUSEL ? TempHeader?.Replace("\\\"", "\"").Replace("\\n", "\n") : string.Empty,
                                TemplateFooter = template.MediaType != MediaType.CAROUSEL ? template.Footer?.Replace("\\\"", "\"").Replace("\\n", "\n") : string.Empty,
                                CallButtonName = template.MediaType != MediaType.CAROUSEL ? phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonName : string.Empty,
                                PhoneNumber = template.MediaType != MediaType.CAROUSEL ? phoneNumberButtons == null ? "" : phoneNumberButtons.ButtonValue : string.Empty,
                                UrlButtonNames = template.MediaType != MediaType.CAROUSEL ? String.Join(",", urlButtons.Select(m => m.ButtonName)) : string.Empty,
                                RedirectUrls = template.MediaType != MediaType.CAROUSEL ? TemptButtonRedirectUrls : string.Empty,
                                QuickReplies = template.MediaType != MediaType.CAROUSEL ? String.Join(",", quickReplyButtons.Select(m => m.ButtonValue)) : string.Empty,
                                To = To,
                                From = model.BusinessId,
                                BusinessId = Guid.TryParse(model.BusinessId, out var tempGuid) ? tempGuid : Guid.Empty,
                                ContactId = contact?.ContactId ?? Guid.Empty,
                                Status = EngagetoEntities.Enums.ConvStatus.sending,
                                ReferenceId = template.TemplateId.ToString(),
                                WhatsAppMessageId = "",
                                MessageType = MessageType.Template,
                                UserId = SentBy,
                                CarouselCards = conversationCarouselCards
                            };
                            var Obj = await appDbContext.Conversations.AddAsync(sendTemplateEntity);
                            if (contact != null)
                            {
                                contact.LastMessageAt = DateTime.UtcNow;
                            }
                            await appDbContext.SaveChangesAsync();

                            List<ConversationDto> conversations = new List<ConversationDto>();
                            List<Conversations> query = new List<Conversations>();
                            EngagetoEntities.Dtos.WebhookDtos.Messages messages = new();
                            var obj = messages.Message(Obj.Entity, query);
                            conversations.Add(obj);
                            // Convert DateTime to string without timezone specifier
                            var data1 = _applicationDBContext.Ahex_CRM_Users.Where(m => m.CompanyId == model.BusinessId.ToString());
                            if (data1 != null)
                            {
                                var UserIds = data1.Select(m => m.Id).ToList();
                                foreach (var UserId in UserIds)
                                {
                                    await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                    await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                }
                            }
                            JObject jsonObject = JObject.Parse(jsonData);
                            var request = new HttpRequestMessage(new HttpMethod("POST"), $"https://graph.facebook.com/v23.0/{Business.PhoneNumberID}/messages");
                            request.Content = new StringContent(jsonData, Encoding.UTF8, "application/json");
                            response = await _client.SendAsync(request);

                            if (response.IsSuccessStatusCode)
                            {
                                using var stream = response.Content.ReadAsStream();
                                using var reader = new StreamReader(stream);
                                using var jsonReader = new JsonTextReader(reader);
                                var result = _serializer.Deserialize<SendMessageResponseDto>(jsonReader);

                                SendStatus status;

                                switch (result.Messages[0].Status)
                                {
                                    case SendStatus.Accepted:
                                        status = SendStatus.Accepted;
                                        break;
                                    case SendStatus.Rejected:
                                        status = SendStatus.Rejected;
                                        break;
                                    default:
                                        // Log or debug the unexpected status value
                                        Console.WriteLine($"Unexpected status value: {result.Messages[0].Status}");
                                        status = SendStatus.Accepted; // Assign a default status
                                        break;
                                }

                                if (result.Contacts != null && result.Contacts.Count > 0 && result.Messages != null && result.Messages.Count > 0)
                                {

                                    Obj.Entity.WhatsAppMessageId = result.Messages[0].WhatsAppMessageId;
                                    Obj.Entity.Status = ConvStatus.sent;
                                }

                                //AppDbContext dbContext = new AppDbContext();
                                appDbContext.Conversations.Update(sendTemplateEntity);
                                await appDbContext.SaveChangesAsync();
                                obj = messages.Message(Obj.Entity, query);
                                conversations = new List<ConversationDto>();
                                conversations.Add(obj);
                                // scheduling the method to update error message after 2 or 3 second
                                //await _scheduledOperation.RunTaskAsync(sendTemplateEntity.Id, 10, CancellationToken.None);

                                if (data1 != null)
                                {
                                    var UserIds = data1.Select(m => m.Id).ToList();
                                    foreach (var UserId in UserIds)
                                    {
                                        await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                        await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    }
                                }

                            }
                            else
                            {
                                Obj.Entity.Status = ConvStatus.failed;
                                appDbContext.Conversations.Update(Obj.Entity);
                                await appDbContext.SaveChangesAsync();

                                query = new List<Conversations>();
                                obj = messages.Message(Obj.Entity, query);
                                conversations = new List<ConversationDto>();
                                conversations.Add(obj);
                                // Convert DateTime to string without timezone specifier
                                if (data1 != null)
                                {
                                    var UserIds = data1.Select(m => m.Id).ToList();
                                    foreach (var UserId in UserIds)
                                    {
                                        await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).ReceiveMessageFromServer(conversations);
                                        await messageHub.Clients.Groups(model.BusinessId.ToString().ToLower() + UserId.ToString().ToLower()).RenderContacts();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            throw new Exception(ex.ToString());
                        }
                    }
                }
            }
            return response;
        }

        public SendHandler SendTemplateS3(SendTemplate model)
        {
            SendHandler HeaderUrl = new SendHandler();
            if (model.MediaFile == null)
            {
                // Handle empty model.MediaFile upload
                return HeaderUrl;
            }

            /* var fileName = model.TemplateId + "." + GetFileExtension(model.MediaFile.FileName);
             var s3Url = await UploadToS3AndGetUrl(model.MediaFile, fileName);*/
            HeaderUrl.S3Url = model.MediaFile;
            return HeaderUrl;
        }


        public static List<CarouselCardsDto> JsonCardsConverter(string carouselCardsJson, List<CarouselCardVariableDto>? carouselVariables)
        {
            if (string.IsNullOrEmpty(carouselCardsJson))
            {
                throw new ArgumentException("CarouselCardsJson cannot be null or empty.");
            }

            List<CarouselCardsDto> cards = new List<CarouselCardsDto>();
            try
            {
                var jsonArray = JsonConvert.DeserializeObject<List<string>>(carouselCardsJson);

                if (jsonArray != null)
                {
                    foreach (var jsonString in jsonArray)
                    {
                        var card = JsonConvert.DeserializeObject<CarouselCardsDto>(jsonString);
                        if (card != null)
                        {
                            cards.Add(card);
                        }
                    }
                }
                else
                {
                    cards = JsonConvert.DeserializeObject<List<CarouselCardsDto>>(carouselCardsJson) ?? new List<CarouselCardsDto>();
                }
            }
            catch (JsonSerializationException ex)
            {
                throw new ArgumentException("Error deserializing CarouselCardsJson. Ensure the JSON matches the expected format.", ex);
            }

            return cards;
        }

        public string SendCarouselCardsPayload(List<CarouselCardsDto> carouselCards, string headerHandle, string HeaderHandle, Template template, List<CarouselCardVariableDto> variables)
        {
            var regex = StringHelper.GetVariableRegexs();
            if (carouselCards == null || !carouselCards.Any())
            {
                throw new Exception("Carousel cards not found!");
            }

            var mediaType = MediaExtentionsHelper.GetMediaTypeFromUrl(template.MediaAwsUrl ?? string.Empty);
            var cards = carouselCards.Select((card, index) =>
            {
                var cardVariables = variables.ElementAtOrDefault(index);
                var bodyParameters = !string.IsNullOrEmpty(card.Body) && cardVariables?.BodyCarouselVariableValues != null
                    ? cardVariables.BodyCarouselVariableValues.Select(value => $@"{{ ""type"": ""text"", ""text"": ""{value}"" }}").ToList()
                    : new List<string>();


                var formateBody = StringHelper.FormateTemplateComponents(card.Body);
                var leadratVarBody = StringHelper.ReplaceAndExtractVariables(formateBody).UpdatedMessage;
                var bodyPayload = regex.IsMatch(leadratVarBody) && bodyParameters.Any()
                    ? $@"{{ ""type"": ""body"",
                            ""parameters"": [{string.Join(",", bodyParameters)}]
                         }}"
                    : string.Empty;

                var headerPayload = !string.IsNullOrEmpty(template.MediaAwsUrl)
            ? $@"{{ ""type"": ""header"",
                     ""parameters"": [{{
                                        ""type"": ""{card.MediaUrlType?.ToString().ToLower()}"",
                                        ""{card.MediaUrlType?.ToString().ToLower()}"": {{ ""link"": ""{card.HeaderMediaUrl}"" }}
                                      }}]
                   }}"
            : string.Empty;


                var buttonsPayload = SendCarouselButtonPayload(card, cardVariables ?? new());

                var cardComponents = new List<string> { headerPayload, bodyPayload, buttonsPayload }
                    .Where(component => !string.IsNullOrEmpty(component))
                    .ToList();

                return $@"{{ ""card_index"": ""{index}"", ""components"": [{string.Join(",", cardComponents)}] }}";
            }).ToList();

            return $@"{{ ""type"": ""carousel"", ""cards"": [{string.Join(",", cards)}] }}";
        }

        private static string SendCarouselButtonPayload(CarouselCardsDto card, CarouselCardVariableDto variables)
        {
            if (card.UrlButtonName?.Count > 0 && card.RedirectUrl?.Count == card.UrlButtonName.Count)
            {
                var buttonsArray = card.UrlButtonName.Select((name, index) => new JObject
                {
                   { "type", "button" },
                   { "sub_type", "url" },
                   { "index", index.ToString() },
                   { "parameters", new JArray  { new JObject
                    {
                        { "type", "text" },
                        { "text", variables?.RedirectUrlVariableValues != null && variables.RedirectUrlVariableValues.Length > index
                            ? variables.RedirectUrlVariableValues[index]
                            : name
                        }
                    }
                }
            }
            }).ToArray();

                return string.Join(",", buttonsArray.Select(x => x.ToString(Newtonsoft.Json.Formatting.None)));
            }
            return string.Empty;
        }

        public static string UpdatedCarouselCardsJson(List<CarouselCardsDto> jsonCardsConverted, List<CarouselCardVariableDto> carouselCardVariables)
        {
            try
            {
                var updatedCards = jsonCardsConverted.Select((card, index) =>
                {
                    if (index < carouselCardVariables.Count)
                    {
                        var carouselVariable = carouselCardVariables[index];

                        if (!string.IsNullOrEmpty(card.Body) && carouselVariable.BodyCarouselVariableValues != null)
                        {
                            var leadratVar = StringHelper.ReplaceAndExtractVariables(card.Body).UpdatedMessage;
                            card.Body = StringHelper.ReplacePlaceholders(leadratVar, carouselVariable.BodyCarouselVariableValues.ToList());

                        }

                        if (card.RedirectUrl != null && carouselVariable.RedirectUrlVariableValues != null)
                        {
                            for (int i = 0; i < card.RedirectUrl.Count(); i++)
                            {
                                if (i < carouselVariable.RedirectUrlVariableValues.Count())
                                {
                                    var leadratUrlVar = StringHelper.ReplaceAndExtractVariables(card.RedirectUrl[i]).UpdatedMessage;
                                    card.RedirectUrl[i] = StringHelper.ReplacePlaceholders(leadratUrlVar, new List<string> { carouselVariable.RedirectUrlVariableValues[i] });
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(carouselVariable.MediaUrl))
                        {
                            card.HeaderMediaUrl = carouselVariable.MediaUrl;
                        }
                    }
                    return card;
                }).ToList();

                var jsonArray = JArray.FromObject(updatedCards);
                return jsonArray.ToString();
            }
            catch (Exception ex)
            {
                return $"Error processing carouselCards: {ex.Message}";
            }
        }
        #endregion
    }
}
