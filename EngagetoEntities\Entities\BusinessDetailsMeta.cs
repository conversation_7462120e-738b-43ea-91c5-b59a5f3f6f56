﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using EngagetoEntities.Enums;

namespace EngagetoEntities.Entities
{
    [Table("BusinessDetails_Metas")]
    public class BusinessDetailsMeta
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        public string AppId { get; set; }
        public string AppName { get; set; }
        public string PhoneNumberID { get; set; }
        public string WhatsAppBusinessAccountID { get; set; }
        public string Token { get; set; }
        public string? BusinessId { get; set; }
        public string? DisplayPhoneNumber { get; set; }
        public QualityScore? QualityRating { get; set; }
        public string? BusinessStatus { get; set; }
        public long? MessageLimit { get; set; }
        public string? Tier { get; set; }
        public DateTime? LastChatStatusUpdated { get; set; }
        public bool IsEnableLiteApi { get; set; }
    }
}
